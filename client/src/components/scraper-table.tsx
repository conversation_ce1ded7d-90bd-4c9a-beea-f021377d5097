import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { formatDistanceToNow } from "date-fns";
import { socket } from "@/lib/socket";
import {
  Table as TableIcon,
  Edit,
  Trash2,
  <PERSON>ota<PERSON><PERSON><PERSON>,
  Loader2
} from "lucide-react";
import { determineUrgency, getUrgencyStyles, formatPrice } from "@/utils/urgency-display";
import { classifyDeal, getDealQualityBadgeClasses } from "@/utils/deal-quality";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import EditScraperDialog from "@/components/edit-scraper-dialog";
import type { Scraper } from "@shared/schema";

export default function ScraperTable() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [editingScraper, setEditingScraper] = useState<Scraper | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const { data: scrapers = [], isLoading } = useQuery<Scraper[]>({
    queryKey: ["/api/scrapers"],
    refetchInterval: autoRefresh ? 30000 : false, // Auto-refresh every 30 seconds when enabled
  });

  useEffect(() => {
    socket.connect();

    function onScraperUpdated(updatedScraper: Scraper) {
      queryClient.setQueryData(["/api/scrapers"], (oldData: Scraper[] | undefined) => {
        if (!oldData) return [];
        return oldData.map(s => s.id === updatedScraper.id ? updatedScraper : s);
      });
    }

    function onScraperError(error: { id: string, message: string }) {
      toast({
        title: "Scraper Error",
        description: `Failed to update scraper ${error.id}: ${error.message}`,
        variant: "destructive",
      });
    }

    socket.on("scraper:updated", onScraperUpdated);
    socket.on("scraper:error", onScraperError);

    return () => {
      socket.off("scraper:updated", onScraperUpdated);
      socket.off("scraper:error", onScraperError);
      socket.disconnect();
    };
  }, [queryClient, toast]);

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      // This still uses REST
      const res = await fetch(`/api/scrapers/${id}`, { method: 'DELETE' });
      if (!res.ok) {
        throw new Error('Failed to delete scraper');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/scrapers"] });
      toast({
        title: "Scraper deleted",
        description: "The scraper has been removed successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete scraper",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateScraper = (id: string) => {
    socket.emit("scraper:update", id);
  };

  const updateAllScrapers = () => {
    socket.emit("scraper:update-all");
  };

  const getStatusIndicator = (scraper: Scraper) => {
    switch (scraper.status) {
      case "active":
        return (
          <span
            className="inline-flex items-center justify-center w-6 h-6 text-xs font-bold rounded-full bg-chart-2/20 text-chart-2"
            title="Active"
          >
            A
          </span>
        );
      case "updating":
        return (
          <span
            className="inline-flex items-center justify-center w-6 h-6 text-xs font-bold rounded-full bg-chart-1/20 text-chart-1"
            title="Updating"
          >
            U
          </span>
        );
      case "error":
        return (
          <span
            className="inline-flex items-center justify-center w-6 h-6 text-xs font-bold rounded-full bg-destructive/20 text-destructive"
            title="Error"
          >
            E
          </span>
        );
      default:
        return null;
    }
  };

  // Using formatPrice from urgency-display utils instead of local function

  const calculatePriceDifference = (current: string | null, comparison: string | null) => {
    if (!current || !comparison) return null;
    const currentPrice = parseFloat(current);
    const comparisonPrice = parseFloat(comparison);
    const difference = currentPrice - comparisonPrice;
    if (comparisonPrice === 0) return { difference: "0.00", percentage: "0" };
    const percentage = ((difference / comparisonPrice) * 100).toFixed(0);
    return { difference: difference.toFixed(2), percentage };
  };

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="shadow-sm">
        <CardHeader className="border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TableIcon className="text-primary" size={20} />
              <h2 className="text-xl font-semibold">Active Scrapers</h2>
              <Badge variant="secondary" className="bg-muted text-muted-foreground">
                {scrapers.length}
              </Badge>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={updateAllScrapers}
              disabled={scrapers.some(s => s.status === 'updating')}
            >
              <RotateCw className="w-4 h-4 mr-2" />
              Update All
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border">
                  <TableHead className="p-2 font-medium text-sm text-muted-foreground w-12">
                    Status
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Deal Quality
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Item
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Current Price & Urgency
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Price Context (ATL)
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Last Updated
                  </TableHead>
                  <TableHead className="p-4 font-medium text-sm text-muted-foreground">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {scrapers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="p-8 text-center text-muted-foreground">
                      No scrapers added yet. Add your first scraper to get started!
                    </TableCell>
                  </TableRow>
                ) : (
                  scrapers.map((scraper) => {
                    const dealClassification = classifyDeal(scraper);
                    const urgencyInfo = determineUrgency(scraper);
                    const lowestPriceDiff = calculatePriceDifference(scraper.currentPrice, scraper.lowestPrice ?? null);
                    return (
                      <TableRow
                        key={scraper.id}
                        className={`border-b border-border hover:bg-muted/50 transition-colors ${
                          scraper.status === "error" ? "error-border" : ""
                        }`}
                        data-testid={`row-scraper-${scraper.id}`}
                      >
                        <TableCell className="p-2 text-center">
                          {getStatusIndicator(scraper)}
                        </TableCell>
                        <TableCell className="p-4">
                          <div className={getDealQualityBadgeClasses(dealClassification)}>
                            {dealClassification.label}
                          </div>
                        </TableCell>
                        <TableCell className="p-4">
                          <div>
                            <div className="font-medium" data-testid={`text-name-${scraper.id}`}>
                              {scraper.itemName}
                            </div>
                            <div
                              className="text-sm text-muted-foreground truncate max-w-xs"
                              data-testid={`text-url-${scraper.id}`}
                            >
                              {scraper.url}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="p-4">
                          <div className="space-y-1">
                            <span
                              className={`font-semibold text-lg ${
                                scraper.status === "error" ? "text-destructive" : ""
                              }`}
                              data-testid={`text-current-price-${scraper.id}`}
                            >
                              {scraper.status === "error" ? "Error" : formatPrice(scraper.currentPrice)}
                            </span>
                            <div className={getUrgencyStyles(urgencyInfo)}>
                              {urgencyInfo.message}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="p-4">
                          <span
                            className="font-medium text-chart-2"
                            data-testid={`text-lowest-price-${scraper.id}`}
                          >
                            ATL: {formatPrice(scraper.lowestPrice)}
                          </span>
                          {lowestPriceDiff && (
                            <div className="text-xs text-muted-foreground">
                              {parseFloat(lowestPriceDiff.difference) >= 0 ? '+' : ''}${lowestPriceDiff.difference} ({parseFloat(lowestPriceDiff.percentage) >= 0 ? '+' : ''}{lowestPriceDiff.percentage}%)
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="p-4">
                          <span 
                            className={`text-sm ${
                              scraper.status === "error" ? "text-destructive" : ""
                            }`}
                            data-testid={`text-last-updated-${scraper.id}`}
                          >
                            {scraper.status === "updating" 
                              ? "Updating..." 
                              : scraper.lastUpdated
                                ? formatDistanceToNow(new Date(scraper.lastUpdated), { addSuffix: true })
                                : "Never"
                            }
                          </span>
                        </TableCell>
                        <TableCell className="p-4">
                          <div className="flex items-center gap-1">
                            <Button
                              size="icon"
                              variant="ghost"
                              data-testid={`button-update-${scraper.id}`}
                              disabled={scraper.status === "updating"}
                              onClick={() => updateScraper(scraper.id)}
                              className="h-8 w-8 hover:text-chart-3"
                              title={scraper.status === "error" ? "Retry update" : "Update price"}
                            >
                              <RotateCw className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              size="icon"
                              variant="ghost"
                              data-testid={`button-edit-${scraper.id}`}
                              onClick={() => setEditingScraper(scraper)}
                              className="h-8 w-8 hover:text-primary"
                              title="Edit scraper"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              size="icon"
                              variant="ghost"
                              data-testid={`button-delete-${scraper.id}`}
                              disabled={deleteMutation.isPending}
                              onClick={() => deleteMutation.mutate(scraper.id)}
                              className="h-8 w-8 hover:text-destructive"
                              title="Delete scraper"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {scrapers.length > 0 && (
            <div className="p-4 border-t border-border bg-muted/30">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>Showing {scrapers.length} scrapers</span>
                <div className="flex items-center gap-2">
                  <Label htmlFor="auto-refresh" className="text-sm">
                    Auto-refresh
                  </Label>
                  <Switch
                    id="auto-refresh"
                    data-testid="toggle-auto-refresh"
                    checked={autoRefresh}
                    onCheckedChange={setAutoRefresh}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <EditScraperDialog
        scraper={editingScraper}
        open={!!editingScraper}
        onOpenChange={(open) => !open && setEditingScraper(null)}
      />
    </>
  );
}
