import type { Scraper } from "@shared/schema";

export interface UrgencyInfo {
  type: "recent-drop" | "recent-increase" | "ongoing-sale" | "stable";
  message: string;
  icon: string;
  color: "green" | "red" | "blue" | "gray";
  priority: number; // Higher = more urgent
}

export interface PriceChangeInfo {
  hasRecentChange: boolean;
  changeAmount: number;
  changeDirection: "increase" | "decrease" | "stable";
  hoursAgo: number;
}

/**
 * Determines what urgency message to display based on scraper data
 * @param scraper Scraper data with current price and sale information
 * @param priceHistory Recent price history for change detection
 * @returns Urgency information for display
 */
export function determineUrgency(
  scraper: Scraper,
  priceHistory?: any[] // We'll get this from an API call
): UrgencyInfo {
  // For now, we'll work with the data we have in the scraper
  // In a full implementation, we'd analyze the price history
  
  const currentPrice = scraper.currentPrice ? parseFloat(scraper.currentPrice) : null;
  const averagePrice = scraper.averagePrice ? parseFloat(scraper.averagePrice) : null;
  
  // Check if item is on sale
  if (scraper.isOnSale && scraper.saleStartDate) {
    const saleStart = new Date(scraper.saleStartDate);
    const now = new Date();
    const hoursAgo = (now.getTime() - saleStart.getTime()) / (1000 * 60 * 60);
    
    // If sale started within last 24 hours, show as recent drop
    if (hoursAgo <= 24) {
      const dropAmount = calculateDropAmount(scraper);
      return {
        type: "recent-drop",
        message: `▼ $${dropAmount.toFixed(2)} (Today)`,
        icon: "▼",
        color: "green",
        priority: 100,
      };
    } else {
      // Show ongoing sale duration
      const days = Math.floor(hoursAgo / 24);
      const daysText = days === 1 ? "1 day" : `${days} days`;
      return {
        type: "ongoing-sale",
        message: `On Sale (${daysText})`,
        icon: "🏷️",
        color: "blue",
        priority: 80,
      };
    }
  }
  
  // Check for recent price increases (this would need price history analysis)
  // For now, we'll use a simple heuristic based on current vs average
  if (currentPrice && averagePrice && currentPrice > averagePrice * 1.1) {
    // If price is significantly above average, it might be a recent increase
    const increaseAmount = currentPrice - averagePrice;
    return {
      type: "recent-increase",
      message: `▲ $${increaseAmount.toFixed(2)} (Recent)`,
      icon: "▲",
      color: "red",
      priority: 60,
    };
  }
  
  // Default to stable
  return {
    type: "stable",
    message: "(Stable)",
    icon: "—",
    color: "gray",
    priority: 10,
  };
}

/**
 * Calculates the drop amount for a sale
 * @param scraper Scraper with sale information
 * @returns Drop amount in dollars
 */
function calculateDropAmount(scraper: Scraper): number {
  const currentPrice = scraper.currentPrice ? parseFloat(scraper.currentPrice) : 0;
  const priceBeforeSale = scraper.priceBeforeSale ? parseFloat(scraper.priceBeforeSale) : 0;
  
  if (priceBeforeSale > currentPrice) {
    return priceBeforeSale - currentPrice;
  }
  
  // Fallback to average price if priceBeforeSale is not available
  const averagePrice = scraper.averagePrice ? parseFloat(scraper.averagePrice) : 0;
  if (averagePrice > currentPrice) {
    return averagePrice - currentPrice;
  }
  
  return 0;
}

/**
 * Gets CSS classes for urgency display styling
 * @param urgency Urgency information
 * @returns CSS classes for styling
 */
export function getUrgencyStyles(urgency: UrgencyInfo): string {
  const baseClasses = "text-sm font-medium";
  
  switch (urgency.color) {
    case "green":
      return `${baseClasses} text-green-600 dark:text-green-400`;
    case "red":
      return `${baseClasses} text-red-600 dark:text-red-400`;
    case "blue":
      return `${baseClasses} text-blue-600 dark:text-blue-400`;
    case "gray":
    default:
      return `${baseClasses} text-gray-500 dark:text-gray-400`;
  }
}

/**
 * Formats a price for display
 * @param price Price string or null
 * @returns Formatted price string
 */
export function formatPrice(price: string | null): string {
  if (!price) return "N/A";
  const numPrice = parseFloat(price);
  if (isNaN(numPrice)) return "N/A";
  return `$${numPrice.toFixed(2)}`;
}

/**
 * Calculates sale duration in human-readable format
 * @param saleStartDate When the sale started
 * @returns Human-readable duration string
 */
export function formatSaleDuration(saleStartDate: Date | string | null): string {
  if (!saleStartDate) return "";
  
  const start = new Date(saleStartDate);
  const now = new Date();
  
  if (isNaN(start.getTime())) return "";
  
  const diffMs = now.getTime() - start.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays >= 1) {
    return diffDays === 1 ? "1 day" : `${diffDays} days`;
  } else if (diffHours >= 1) {
    return diffHours === 1 ? "1 hour" : `${diffHours} hours`;
  } else {
    return "Less than 1 hour";
  }
}

/**
 * Determines if a price change is significant enough to highlight
 * @param changeAmount Absolute change amount
 * @param changePercentage Percentage change
 * @returns Whether the change is significant
 */
export function isSignificantChange(
  changeAmount: number,
  changePercentage: number
): boolean {
  const minAmount = 10; // $10 minimum
  const minPercentage = 2; // 2% minimum
  
  return Math.abs(changeAmount) >= minAmount || Math.abs(changePercentage) >= minPercentage;
}

/**
 * Sorts scrapers by urgency priority (most urgent first)
 * @param scrapers Array of scrapers with urgency info
 * @returns Sorted array with most urgent first
 */
export function sortByUrgency<T extends { urgency?: UrgencyInfo }>(
  scrapers: T[]
): T[] {
  return [...scrapers].sort((a, b) => {
    const priorityA = a.urgency?.priority ?? 0;
    const priorityB = b.urgency?.priority ?? 0;
    return priorityB - priorityA; // Higher priority first
  });
}
