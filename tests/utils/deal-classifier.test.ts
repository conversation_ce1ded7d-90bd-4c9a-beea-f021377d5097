import { describe, it, expect } from 'vitest';
import { classifyDeal, getDealQualityStyles, sortByDealQuality } from '../../server/src/utils/deal-classifier';

describe('Deal Classifier', () => {
  describe('classifyDeal', () => {
    it('should classify all-time low correctly', () => {
      const classification = classifyDeal('100.00', '100.00', '120.00');
      
      expect(classification.quality).toBe('all-time-low');
      expect(classification.label).toBe('★ All-Time Low');
      expect(classification.color).toBe('gold');
      expect(classification.priority).toBe(100);
    });

    it('should classify excellent deal (20%+ below average)', () => {
      const classification = classifyDeal('95.00', '90.00', '120.00');
      
      expect(classification.quality).toBe('excellent-deal');
      expect(classification.label).toBe('Excellent Deal');
      expect(classification.color).toBe('green');
      expect(classification.priority).toBe(90);
      expect(classification.description).toContain('21% below average');
    });

    it('should classify good deal (10-20% below average)', () => {
      const classification = classifyDeal('105.00', '100.00', '120.00');

      expect(classification.quality).toBe('good-deal');
      expect(classification.label).toBe('Good Deal');
      expect(classification.color).toBe('blue');
      expect(classification.priority).toBe(70);
      expect(classification.description).toContain('12% below average');
    });

    it('should classify fair price (within 10% of average)', () => {
      const classification = classifyDeal('125.00', '100.00', '120.00');
      
      expect(classification.quality).toBe('fair-price');
      expect(classification.label).toBe('Fair Price');
      expect(classification.color).toBe('gray');
      expect(classification.priority).toBe(50);
    });

    it('should classify high price (10-25% above average)', () => {
      const classification = classifyDeal('140.00', '100.00', '120.00');
      
      expect(classification.quality).toBe('high-price');
      expect(classification.label).toBe('High Price');
      expect(classification.color).toBe('orange');
      expect(classification.priority).toBe(30);
      expect(classification.description).toContain('17% above average');
    });

    it('should classify very high price (25%+ above average)', () => {
      const classification = classifyDeal('160.00', '100.00', '120.00');
      
      expect(classification.quality).toBe('very-high-price');
      expect(classification.label).toBe('Very High Price');
      expect(classification.color).toBe('red');
      expect(classification.priority).toBe(10);
      expect(classification.description).toContain('33% above average');
    });

    it('should handle missing average price', () => {
      const classification = classifyDeal('105.00', '100.00', null);

      expect(classification.quality).toBe('excellent-deal');
      expect(classification.label).toBe('Excellent Deal');
      expect(classification.description).toBe('Very close to all-time low');
    });

    it('should handle missing current price', () => {
      const classification = classifyDeal(null, '100.00', '120.00');
      
      expect(classification.quality).toBe('unknown');
      expect(classification.label).toBe('Unknown');
      expect(classification.color).toBe('gray');
    });

    it('should handle invalid prices', () => {
      const classification = classifyDeal('invalid', '100.00', '120.00');
      
      expect(classification.quality).toBe('unknown');
    });

    it('should classify excellent deal when very close to ATL without average', () => {
      const classification = classifyDeal('102.00', '100.00', null);
      
      expect(classification.quality).toBe('excellent-deal');
      expect(classification.description).toBe('Very close to all-time low');
    });
  });

  describe('getDealQualityStyles', () => {
    it('should return correct CSS classes for each color', () => {
      const goldClassification = { quality: 'all-time-low' as const, label: 'ATL', description: '', color: 'gold' as const, priority: 100 };
      const styles = getDealQualityStyles(goldClassification);
      
      expect(styles.badge).toContain('bg-yellow-100');
      expect(styles.badge).toContain('text-yellow-800');
      expect(styles.text).toContain('text-yellow-800');
    });

    it('should include dark mode classes', () => {
      const greenClassification = { quality: 'excellent-deal' as const, label: 'Excellent', description: '', color: 'green' as const, priority: 90 };
      const styles = getDealQualityStyles(greenClassification);
      
      expect(styles.badge).toContain('dark:bg-green-900');
      expect(styles.badge).toContain('dark:text-green-200');
    });
  });

  describe('sortByDealQuality', () => {
    it('should sort by priority (highest first)', () => {
      const items = [
        { id: 1, dealClassification: { quality: 'fair-price' as const, label: '', description: '', color: 'gray' as const, priority: 50 } },
        { id: 2, dealClassification: { quality: 'all-time-low' as const, label: '', description: '', color: 'gold' as const, priority: 100 } },
        { id: 3, dealClassification: { quality: 'high-price' as const, label: '', description: '', color: 'orange' as const, priority: 30 } },
      ];

      const sorted = sortByDealQuality(items);
      
      expect(sorted[0].id).toBe(2); // ATL (priority 100)
      expect(sorted[1].id).toBe(1); // Fair price (priority 50)
      expect(sorted[2].id).toBe(3); // High price (priority 30)
    });

    it('should handle items without deal classification', () => {
      const items = [
        { id: 1 },
        { id: 2, dealClassification: { quality: 'all-time-low' as const, label: '', description: '', color: 'gold' as const, priority: 100 } },
      ];

      const sorted = sortByDealQuality(items);
      
      expect(sorted[0].id).toBe(2); // Has classification
      expect(sorted[1].id).toBe(1); // No classification (priority 0)
    });
  });
});
